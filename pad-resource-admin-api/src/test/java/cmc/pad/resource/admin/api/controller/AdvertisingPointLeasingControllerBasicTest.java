package cmc.pad.resource.admin.api.controller;

import org.junit.Assert;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 宣传点位租赁价格控制器基础测试
 * 用于记录重构前的API响应基准数据
 *
 * <AUTHOR>
 * @Date 2025/09/01
 * @Version 1.0
 */
public class AdvertisingPointLeasingControllerBasicTest {

    private static final String BASE_URL = "http://localhost:8125";

    /**
     * 发送HTTP GET请求
     */
    private String httpGet(String path) throws Exception {
        URL url = new URL(BASE_URL + path);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("GET");
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);

        int responseCode = conn.getResponseCode();
        BufferedReader reader;

        if (responseCode >= 200 && responseCode < 300) {
            reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        } else {
            reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
        }

        StringBuilder response = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            response.append(line);
        }
        reader.close();
        conn.disconnect();

        return response.toString();
    }

    @Test
    public void testParamCinemaInnerCodeIsNullButCityLevelAndCinemaLevelIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi(""), 2006, "影城级别不存在");
        assertResponseStatusAndMsg(getApi("city_level=1"), 101, "city_level城市级别错误");
        assertResponseStatusAndMsg(getApi("city_level=L1"), 2006, "影城级别不存在");
        assertResponseStatusAndMsg(getApi("cinema_level=a"), 2006, "影城级别不存在");
    }

    @Test
    public void testParamCinemaInnerCodeIsNotNull() throws Exception {
        assertResponseStatusAndMsg(getApi("cinema_code=849"), 0, "OK");
        assertResponseStatusAndMsg(getApi("cinema_code=304"), 0, "OK", "[{\"city_level\":\"L1\",\"cinema_level\":\"A\",\"base_price\":2.35,\"base_area\":1,\"extended_price\":2.33}]");
    }

    private String getApi(String param) throws Exception {
        String path = "/advertising-point-leasing/price/query?" + param;
        System.out.println(path);
        return httpGet(path);
    }

    private void assertResponseStatusAndMsg(String response, int status, String msg, String... data) {
        Assert.assertTrue("status:" + status + " msg: " + msg + ", but response: " + response, response.contains("\"status\":" + status));
        Assert.assertTrue("msg: " + msg + ", but response: " + response, response.contains("\"msg\":\"" + msg + "\""));
        if (data != null && data.length != 0)
            Assert.assertTrue("data: " + data[0] + ", but response: " + response, response.contains("\"data\":" + data[0]));
    }
}